# Generated manually to remove Type model and type field from Template

from django.db import migrations


class Migration(migrations.Migration):

    dependencies = [
        ('services', '0002_type_template'),
    ]

    operations = [
        # First remove the unique_together constraint that includes 'type'
        migrations.AlterUniqueTogether(
            name='template',
            unique_together=set(),
        ),

        # Remove the type field from Template model
        migrations.RemoveField(
            model_name='template',
            name='type',
        ),

        # Delete the Type model
        migrations.DeleteModel(
            name='Type',
        ),
    ]
