# Generated by Django 5.2 on 2025-07-29 09:26

import services.models
import services.validators
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('services', '0003_remove_type_model'),
    ]

    operations = [
        migrations.AlterField(
            model_name='template',
            name='demo_video',
            field=models.FileField(blank=True, help_text='Demo video (MP4, AVI, MOV, WMV - max 200MB)', null=True, upload_to=services.models.demo_video_upload_path, validators=[services.validators.validate_demo_video]),
        ),
        migrations.AlterField(
            model_name='template',
            name='file',
            field=models.FileField(help_text='Template file (PDF, DOC, DOCX, TXT, PPT, PPTX - max 200MB)', upload_to=services.models.template_file_upload_path, validators=[services.validators.validate_template_file]),
        ),
        migrations.AlterUniqueTogether(
            name='template',
            unique_together={('title', 'service')},
        ),
    ]
