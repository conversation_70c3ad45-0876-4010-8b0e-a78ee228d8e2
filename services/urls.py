from django.urls import path, include
from rest_framework.routers import <PERSON>fault<PERSON>outer
from . import views

# Create a router for all ViewSets
router = DefaultRouter()
router.register(r'services', views.ServiceViewSet, basename='services')
router.register(r'templates', views.TemplateViewSet, basename='templates')

app_name = 'services'

urlpatterns = [
    # Public endpoints (no authentication required)
    path('public/templates/', views.PublicTemplateListView.as_view(), name='public-templates'),

    # Include all router URLs (authenticated endpoints)
    path('', include(router.urls)),
]
